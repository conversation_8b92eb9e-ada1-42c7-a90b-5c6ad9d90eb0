@import url('https://fonts.googleapis.com/css2?family=Inter:wght@200;400;500&display=swap');

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body{
    width: 100vw;
    height: 100vh;
    overflow-x: hidden;
    /* padding-top: 65px; */
    color: #424242;
    min-width: 350px
}


#main{
    width: 100%;
    height: 100%;
    position: relative;
} 

.nav{
    width: 100%;
    height: 65px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    position: fixed;
    /* margin-top: -10vh; */
    z-index: 9999;
    margin-bottom: 30px;
    background-color: #fff;
    box-shadow: 2px 1px 2px #eee;
    box-sizing: border-box;
}

.container-1{
    position: relative;
    width: 1100px;
    display: flex;
    height: 100%;
    background-color: #fff;
    max-width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
}

.logo{
    width: 33.9999999%;
    height: 65px;
    display: flex;
    align-items: center;
    justify-content: start;

}

.logo img{
    max-height: 17px;
    cursor: pointer;
    vertical-align: middle;
    position: relative;

}

.-left-sid{
    width: 64.6666666667%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.opt{
    width: 90%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: end;
}

.opt ul{
    display: flex;
    justify-content: space-between;
}

.opt ul li{
    list-style: none;
    padding-left: 35px;
    display: flex;
    justify-content: space-between;
}

.opt ul li a{
    text-decoration: none;
    font-family: "inter",serif;
    color: #666;
    font-weight: 500;
    font-size: 15px;
    transition: 0.5 ease;
    

}

.opt ul li a:hover{
    color: #387ed1;
}


.menu-i{
    width: 10%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-i a{
    color: #666;
    font-size: 20px;
    font-weight: 400;
    margin-top: 5px;
}

#homepage{

    position: relative;
    padding: 0 20px 120px 20px ;
}

.container-01{
    position: relative;
    width: 1100px;
    max-width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
}

