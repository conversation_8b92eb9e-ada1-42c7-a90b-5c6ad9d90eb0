import { useState } from "react";

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className="w-full bg-white border-b border-gray-100 fixed top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-14">
          {/* Logo */}
          <div className="flex items-center">
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
                <span className="text-white text-xs font-bold">Z</span>
              </div>
              <span className="text-lg font-semibold text-gray-900 tracking-wide">
                ZERODHA
              </span>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <a
              href="#"
              className="text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              Signup
            </a>
            <a
              href="#"
              className="text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              About
            </a>
            <a
              href="#"
              className="text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              Products
            </a>
            <a
              href="#"
              className="text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              Pricing
            </a>
            <a
              href="#"
              className="text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              Support
            </a>

            {/* Menu Icon */}
            <button className="text-gray-600 hover:text-gray-900 transition-colors duration-200 p-1">
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="text-gray-600 hover:text-gray-900 transition-colors duration-200 p-2"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white border-t border-gray-100 shadow-lg">
          <div className="px-4 py-2 space-y-1">
            <a
              href="#"
              className="block px-3 py-2 text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              Signup
            </a>
            <a
              href="#"
              className="block px-3 py-2 text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              About
            </a>
            <a
              href="#"
              className="block px-3 py-2 text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              Products
            </a>
            <a
              href="#"
              className="block px-3 py-2 text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              Pricing
            </a>
            <a
              href="#"
              className="block px-3 py-2 text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              Support
            </a>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navigation;
