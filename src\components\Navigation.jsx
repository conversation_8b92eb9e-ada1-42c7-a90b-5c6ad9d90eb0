import { useState } from "react";

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const products = [
    {
      name: "Kite",
      description: "Trading platform",
      image: "https://zerodha.com/static/images/products/kite-logo.svg",
      url: "https://kite.zerodha.com",
    },
    {
      name: "Console",
      description: "Backoffice",
      image: "https://zerodha.com/static/images/products/console.svg",
      url: "https://console.zerodha.com",
    },
    {
      name: "Kite Connect",
      description: "Trading APIs",
      image: "https://zerodha.com/static/images/products/kite-connect.svg",
      url: "/products/api/",
    },
    {
      name: "Coin",
      description: "Mutual funds",
      image: "https://zerodha.com/static/images/products/coin.svg",
      url: "https://coin.zerodha.com",
    },
  ];

  const educationProducts = [
    {
      name: "<PERSON>",
      image: "https://zerodha.com/static/images/products/varsity.png",
      url: "https://zerodha.com/varsity/",
    },
    {
      name: "Trading Q&A",
      image: "https://zerodha.com/static/images/products/tqna.png",
      url: "https://tradingqna.com/",
    },
  ];

  return (
    <nav className="w-full bg-white fixed top-0 z-10 shadow-sm">
      <div className="container mx-auto px-0">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div className="relative w-5/12">
            <a href="/">
              <img
                src="https://zerodha.com/static/images/logo.svg"
                alt="Zerodha logo"
                className="max-h-[17px] align-middle relative top-[18px]"
              />
            </a>
          </div>

          {/* Desktop Navigation */}
          <div
            className="w-7/12 hidden lg:block"
            role="navigation"
            aria-label="main navigation"
          >
            <ul className="list-none text-right m-0 p-0">
              <li className="inline">
                <a
                  href="https://zerodha.com/open-account/"
                  className="text-[#666] text-[0.9rem] transition-colors duration-300 inline-block py-5 px-5 box-border hover:text-[#387ed1]"
                >
                  Signup
                </a>
              </li>
              <li className="inline">
                <a
                  href="https://zerodha.com/about/"
                  className="text-[#666] text-[0.9rem] transition-colors duration-300 inline-block py-5 px-5 box-border hover:text-[#387ed1]"
                >
                  About
                </a>
              </li>
              <li className="inline">
                <a
                  href="https://zerodha.com/products/"
                  className="text-[#666] text-[0.9rem] transition-colors duration-300 inline-block py-5 px-5 box-border hover:text-[#387ed1]"
                >
                  Products
                </a>
              </li>
              <li className="inline">
                <a
                  href="https://zerodha.com/pricing/"
                  className="text-[#666] text-[0.9rem] transition-colors duration-300 inline-block py-5 px-5 box-border hover:text-[#387ed1]"
                >
                  Pricing
                </a>
              </li>
              <li className="inline">
                <a
                  href="https://support.zerodha.com"
                  className="text-[#666] text-[0.9rem] transition-colors duration-300 inline-block py-5 px-5 box-border hover:text-[#387ed1]"
                >
                  Support
                </a>
              </li>

              {/* Menu Button */}
              <li id="navbar_menu" className="inline pt-5 pb-5 pl-[15px]">
                <div
                  className="cursor-pointer inline-block py-5 px-0 text-[#666] font-normal hover:cursor-pointer"
                  id="menu_btn"
                  title="Menu"
                  onClick={toggleMenu}
                >
                  <div
                    id="nav-icon3"
                    className={`w-[18px] top-2 relative mx-auto transform rotate-0 transition-all duration-500 ease-in-out cursor-pointer ${
                      isMenuOpen ? "open" : ""
                    }`}
                  >
                    <span className="block absolute h-0.5 w-full bg-[#424242] rounded-[9px] opacity-100 left-0 transform rotate-0 transition-all duration-[0.25s] ease-in-out top-0"></span>
                    <span className="block absolute h-0.5 w-full bg-[#424242] rounded-[9px] opacity-100 left-0 transform rotate-0 transition-all duration-[0.25s] ease-in-out top-[6px]"></span>
                    <span className="block absolute h-0.5 w-full bg-[#424242] rounded-[9px] opacity-100 left-0 transform rotate-0 transition-all duration-[0.25s] ease-in-out top-[6px]"></span>
                    <span className="block absolute h-0.5 w-full bg-[#424242] rounded-[9px] opacity-100 left-0 transform rotate-0 transition-all duration-[0.25s] ease-in-out top-3"></span>
                  </div>
                </div>
              </li>
            </ul>
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <div
              className="cursor-pointer inline-block py-5 px-0 text-[#666] font-normal absolute right-0 top-1 z-[101]"
              id="menu_btn"
              title="Menu"
              onClick={toggleMenu}
            >
              <div
                id="nav-icon3"
                className={`w-[25px] -top-px relative mx-auto transform rotate-0 transition-all duration-500 ease-in-out cursor-pointer ${
                  isMenuOpen ? "open" : ""
                }`}
              >
                <span
                  className={`block absolute h-0.5 w-full bg-[#424242] rounded-[9px] opacity-100 left-0 transform transition-all duration-[0.25s] ease-in-out ${
                    isMenuOpen ? "top-[18px] w-0 left-1/2" : "top-0 rotate-0"
                  }`}
                ></span>
                <span
                  className={`block absolute h-0.5 w-full bg-[#424242] rounded-[9px] opacity-100 left-0 transform transition-all duration-[0.25s] ease-in-out ${
                    isMenuOpen ? "top-[7px] rotate-45" : "top-[7px] rotate-0"
                  }`}
                ></span>
                <span
                  className={`block absolute h-0.5 w-full bg-[#424242] rounded-[9px] opacity-100 left-0 transform transition-all duration-[0.25s] ease-in-out ${
                    isMenuOpen ? "top-[7px] -rotate-45" : "top-[7px] rotate-0"
                  }`}
                ></span>
                <span
                  className={`block absolute h-0.5 w-full bg-[#424242] rounded-[9px] opacity-100 left-0 transform transition-all duration-[0.25s] ease-in-out ${
                    isMenuOpen
                      ? "top-[18px] w-0 left-1/2"
                      : "top-[14px] rotate-0"
                  }`}
                ></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dropdown Menu */}
      <div
        id="menu"
        className={`navbar-menu text-left ${isMenuOpen ? "show" : ""} ${
          isMenuOpen
            ? "lg:w-[700px] lg:absolute lg:opacity-100 lg:visible lg:transform lg:rotateX-0 lg:right-0 lg:top-[67px] lg:z-100 lg:bg-white lg:rounded-sm lg:shadow-[0px_1px_10px_0px_rgba(0,0,0,0.1)] lg:transition-all lg:duration-150 lg:ease-in-out"
            : "lg:w-[700px] lg:absolute lg:opacity-0 lg:invisible lg:transform lg:-rotate-x-15 lg:origin-top-left lg:right-0 lg:top-[67px] lg:z-100 lg:bg-white lg:rounded-sm lg:shadow-[0px_1px_10px_0px_rgba(0,0,0,0.1)] lg:transition-all lg:duration-150 lg:ease-in-out"
        } ${
          isMenuOpen
            ? "lg:hidden:overflow-auto lg:hidden:top-[10px] lg:hidden:w-[calc(100%+20px)] lg:hidden:left-[-10px] lg:hidden:pt-[15px] lg:hidden:shadow-[0_50px_100px_-20px_rgba(50,50,93,0.25),0_30px_60px_-30px_rgba(0,0,0,0.3),0_-18px_60px_-10px_rgba(0,0,0,0.025)] lg:hidden:rounded-[3px]"
            : "lg:hidden:hidden"
        }`}
      >
        {/* Mobile Navigation Links */}
        <ul className="lg:hidden navbar-links show-on-mobile p-0 m-0 flex flex-wrap px-5 pb-5 pt-0">
          <li className="m-0 text-left flex-[0_0_50%] box-border p-[7px]">
            <a
              href="https://zerodha.com/open-account/"
              className="p-0 text-[0.9rem] text-[#444]"
            >
              Signup
            </a>
          </li>
          <li className="m-0 text-left flex-[0_0_50%] box-border p-[7px] hidden">
            <a
              href="https://zerodha.com/open-account/"
              className="p-0 text-[0.9rem] text-[#444]"
            >
              Open account
            </a>
          </li>
          <li className="m-0 text-left flex-[0_0_50%] box-border p-[7px]">
            <a
              href="https://zerodha.com/about/"
              className="p-0 text-[0.9rem] text-[#444]"
            >
              About
            </a>
          </li>
          <li className="m-0 text-left flex-[0_0_50%] box-border p-[7px]">
            <a
              href="https://zerodha.com/products/"
              className="p-0 text-[0.9rem] text-[#444]"
            >
              Products
            </a>
          </li>
          <li className="m-0 text-left flex-[0_0_50%] box-border p-[7px]">
            <a
              href="https://zerodha.com/pricing/"
              className="p-0 text-[0.9rem] text-[#444]"
            >
              Pricing
            </a>
          </li>
          <li className="m-0 text-left flex-[0_0_50%] box-border p-[7px]">
            <a
              href="https://support.zerodha.com"
              target="_blank"
              rel="noopener noreferrer"
              className="p-0 text-[0.9rem] text-[#444]"
            >
              Support
            </a>
          </li>
        </ul>

        {/* Products Grid - Mobile */}
        <div className="lg:hidden products-list px-5 border-t border-[#eee]">
          <div className="flex justify-between items-center">
            {products.map((product, index) => (
              <div
                key={index}
                className="w-1/2 min-w-[50%] mb-0 flex-[0_0_50%] mt-[15px]"
              >
                <a href={product.url} className="flex items-center">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="max-h-[30px] mr-[5px]"
                  />
                  <span className="font-semibold text-gray-900 text-sm">
                    {product.name}
                  </span>
                </a>
              </div>
            ))}
          </div>

          {/* Education Products - Mobile */}
          <div className="flex justify-between items-center mt-4">
            {educationProducts.map((product, index) => (
              <div
                key={index}
                className="w-1/2 min-w-[50%] mb-0 flex-[0_0_50%] mt-[15px]"
              >
                <a href={product.url} className="flex items-center">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-[18px] mr-[10px] ml-[7px]"
                  />
                  <span className="font-semibold text-gray-900 text-sm">
                    {product.name}
                  </span>
                </a>
              </div>
            ))}
          </div>
        </div>

        {/* Desktop Products Grid */}
        <div className="hidden lg:block products-list py-5">
          <div className="flex justify-between px-5">
            {products.map((product, index) => (
              <a
                key={index}
                href={product.url}
                className="text-center block p-0"
              >
                <img
                  src={product.image}
                  alt={product.name}
                  className="max-h-[50px] transition-opacity duration-300 w-10 mx-auto mb-5"
                />
                <div className="font-semibold text-gray-900 text-sm mb-1">
                  {product.name}
                </div>
                <div className="text-xs text-gray-500">
                  {product.description}
                </div>
              </a>
            ))}
          </div>
        </div>

        {/* Menu Footer */}
        <div className="menu-footer px-[30px] py-5 bg-[#fafafb] lg:block hidden">
          <div className="flex justify-between">
            <div className="w-7/12">
              <div className="flex justify-between">
                <div className="w-1/2">
                  <p className="font-semibold text-gray-900 mb-3">Utilities</p>
                  <a
                    href="https://zerodha.com/calculators/"
                    className="block p-0 mb-[5px] text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                  >
                    Calculators
                  </a>
                  <a
                    href="https://zerodha.com/brokerage-calculator/"
                    className="block p-0 mb-[5px] text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                  >
                    Brokerage calculator
                  </a>
                  <a
                    href="https://zerodha.com/margin-calculator/"
                    className="block p-0 mb-[5px] text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                  >
                    Margin calculator
                  </a>
                  <a
                    href="https://zerodha.com/calculators/sip-calculator/"
                    className="block p-0 mb-[5px] text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                  >
                    SIP calculator
                  </a>
                </div>
                <div className="w-1/2">
                  <p className="font-semibold text-gray-900 mb-3">Updates</p>
                  <a
                    href="https://zerodha.com/z-connect/"
                    className="block p-0 mb-[5px] text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                  >
                    Z-Connect blog
                  </a>
                  <a
                    href="https://zerodha.com/marketintel/bulletin/"
                    className="block p-0 mb-[5px] text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                  >
                    Circulars / Bulletin
                  </a>
                  <a
                    href="https://zerodha.com/ipo/"
                    className="block p-0 mb-[5px] text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                  >
                    IPOs
                  </a>
                  <a
                    href="https://zerodha.com/markets"
                    className="block p-0 mb-[5px] text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                  >
                    Markets
                  </a>
                </div>
              </div>
            </div>

            {/* Education - Desktop */}
            <div className="w-5/12 education">
              <p className="font-semibold text-gray-900 mb-3">Education</p>
              <div className="flex">
                {educationProducts.map((product, index) => (
                  <a
                    key={index}
                    href={product.url}
                    className="text-center mr-10 inline-block"
                  >
                    <img
                      src={product.image}
                      alt={product.name}
                      className="max-h-[35px] mb-[10px]"
                    />
                    <div className="text-sm text-gray-900">{product.name}</div>
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Menu Footer */}
        <div className="lg:hidden menu-footer px-5 py-5 bg-white border-t border-[#eee]">
          <div className="grid grid-cols-1 gap-6">
            <div className="grid grid-cols-2 gap-6">
              <div>
                <p className="font-semibold text-gray-900 mb-3 mt-0">
                  Utilities
                </p>
                <a
                  href="https://zerodha.com/calculators/"
                  className="block p-0 text-[#666]"
                >
                  Calculators
                </a>
                <a
                  href="https://zerodha.com/brokerage-calculator/"
                  className="block p-0 text-[#666]"
                >
                  Brokerage calculator
                </a>
                <a
                  href="https://zerodha.com/margin-calculator/"
                  className="block p-0 text-[#666]"
                >
                  Margin calculator
                </a>
                <a
                  href="https://zerodha.com/calculators/sip-calculator/"
                  className="block p-0 text-[#666]"
                >
                  SIP calculator
                </a>
              </div>
              <div>
                <p className="font-semibold text-gray-900 mb-3 mt-0">Updates</p>
                <a
                  href="https://zerodha.com/z-connect/"
                  className="block p-0 text-[#666]"
                >
                  Z-Connect blog
                </a>
                <a
                  href="https://zerodha.com/marketintel/bulletin/"
                  className="block p-0 text-[#666]"
                >
                  Circulars / Bulletin
                </a>
                <a
                  href="https://zerodha.com/ipo/"
                  className="block p-0 text-[#666]"
                >
                  IPOs
                </a>
                <a
                  href="https://zerodha.com/markets"
                  className="block p-0 text-[#666]"
                >
                  Markets
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
