import { useState } from "react";

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const products = [
    {
      name: "Kite",
      description: "Trading platform",
      image: "https://zerodha.com/static/images/products/kite-logo.svg",
      url: "https://kite.zerodha.com",
    },
    {
      name: "Console",
      description: "Backoffice",
      image: "https://zerodha.com/static/images/products/console.svg",
      url: "https://console.zerodha.com",
    },
    {
      name: "Kite Connect",
      description: "Trading APIs",
      image: "https://zerodha.com/static/images/products/kite-connect.svg",
      url: "/products/api/",
    },
    {
      name: "Coin",
      description: "Mutual funds",
      image: "https://zerodha.com/static/images/products/coin.svg",
      url: "https://coin.zerodha.com",
    },
  ];

  const educationProducts = [
    {
      name: "<PERSON>",
      image: "https://zerodha.com/static/images/products/varsity.png",
      url: "https://zerodha.com/varsity/",
    },
    {
      name: "Trading Q&A",
      image: "https://zerodha.com/static/images/products/tqna.png",
      url: "https://tradingqna.com/",
    },
  ];

  return (
    <nav className="w-full bg-white border-b border-gray-100 relative z-50">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center w-5/12">
            <a href="/">
              <img
                src="https://zerodha.com/static/images/logo.svg"
                alt="Zerodha logo"
                className="h-5"
              />
            </a>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center justify-end w-7/12 space-x-8">
            <a
              href="https://zerodha.com/open-account/"
              className="text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              Signup
            </a>
            <a
              href="https://zerodha.com/about/"
              className="text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              About
            </a>
            <a
              href="https://zerodha.com/products/"
              className="text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              Products
            </a>
            <a
              href="https://zerodha.com/pricing/"
              className="text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              Pricing
            </a>
            <a
              href="https://support.zerodha.com"
              className="text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
            >
              Support
            </a>

            {/* Menu Button */}
            <div className="relative">
              <button
                onClick={toggleMenu}
                className="text-gray-600 hover:text-gray-900 transition-colors duration-200 p-2"
                title="Menu"
              >
                <div className="w-5 h-4 flex flex-col justify-between">
                  <span className="w-full h-0.5 bg-current transition-all duration-300"></span>
                  <span className="w-full h-0.5 bg-current transition-all duration-300"></span>
                  <span className="w-full h-0.5 bg-current transition-all duration-300"></span>
                  <span className="w-full h-0.5 bg-current transition-all duration-300"></span>
                </div>
              </button>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <button
              onClick={toggleMenu}
              className="text-gray-600 hover:text-gray-900 transition-colors duration-200 p-2"
            >
              <div className="w-5 h-4 flex flex-col justify-between">
                <span className="w-full h-0.5 bg-current transition-all duration-300"></span>
                <span className="w-full h-0.5 bg-current transition-all duration-300"></span>
                <span className="w-full h-0.5 bg-current transition-all duration-300"></span>
                <span className="w-full h-0.5 bg-current transition-all duration-300"></span>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Dropdown Menu */}
      {isMenuOpen && (
        <div className="absolute top-full left-0 w-full bg-white border-t border-gray-100 shadow-lg z-40">
          <div className="max-w-7xl mx-auto px-4 py-6">
            {/* Mobile Navigation Links */}
            <div className="lg:hidden mb-6">
              <div className="space-y-2">
                <a
                  href="https://zerodha.com/open-account/"
                  className="block px-3 py-2 text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
                >
                  Signup
                </a>
                <a
                  href="https://zerodha.com/open-account/"
                  className="block px-3 py-2 text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
                >
                  Open account
                </a>
                <a
                  href="https://zerodha.com/about/"
                  className="block px-3 py-2 text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
                >
                  About
                </a>
                <a
                  href="https://zerodha.com/products/"
                  className="block px-3 py-2 text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
                >
                  Products
                </a>
                <a
                  href="https://zerodha.com/pricing/"
                  className="block px-3 py-2 text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
                >
                  Pricing
                </a>
                <a
                  href="https://support.zerodha.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block px-3 py-2 text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors duration-200"
                >
                  Support
                </a>
              </div>
            </div>

            {/* Products Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
              {products.map((product, index) => (
                <a
                  key={index}
                  href={product.url}
                  className="text-center group hover:bg-gray-50 p-4 rounded-lg transition-colors duration-200"
                >
                  <img
                    src={product.image}
                    alt={product.name}
                    className="h-12 mx-auto mb-2"
                  />
                  <div className="font-semibold text-gray-900 text-sm mb-1">
                    {product.name}
                  </div>
                  <div className="text-xs text-gray-500 hidden md:block">
                    {product.description}
                  </div>
                </a>
              ))}

              {/* Education Products - Mobile Only */}
              <div className="col-span-2 md:hidden">
                <div className="grid grid-cols-2 gap-4">
                  {educationProducts.map((product, index) => (
                    <a
                      key={index}
                      href={product.url}
                      className="text-center group hover:bg-gray-50 p-4 rounded-lg transition-colors duration-200"
                    >
                      <img
                        src={product.image}
                        alt={product.name}
                        className="h-12 mx-auto mb-2"
                      />
                      <div className="font-semibold text-gray-900 text-sm">
                        {product.name}
                      </div>
                    </a>
                  ))}
                </div>
              </div>
            </div>

            {/* Footer Section */}
            <div className="border-t border-gray-100 pt-6">
              <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
                {/* Utilities and Updates */}
                <div className="md:col-span-7">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-3">
                        Utilities
                      </h3>
                      <div className="space-y-2">
                        <a
                          href="https://zerodha.com/calculators/"
                          className="block text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                        >
                          Calculators
                        </a>
                        <a
                          href="https://zerodha.com/brokerage-calculator/"
                          className="block text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                        >
                          Brokerage calculator
                        </a>
                        <a
                          href="https://zerodha.com/margin-calculator/"
                          className="block text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                        >
                          Margin calculator
                        </a>
                        <a
                          href="https://zerodha.com/calculators/sip-calculator/"
                          className="block text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                        >
                          SIP calculator
                        </a>
                      </div>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-3">
                        Updates
                      </h3>
                      <div className="space-y-2">
                        <a
                          href="https://zerodha.com/z-connect/"
                          className="block text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                        >
                          Z-Connect blog
                        </a>
                        <a
                          href="https://zerodha.com/marketintel/bulletin/"
                          className="block text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                        >
                          Circulars / Bulletin
                        </a>
                        <a
                          href="https://zerodha.com/ipo/"
                          className="block text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                        >
                          IPOs
                        </a>
                        <a
                          href="https://zerodha.com/markets"
                          className="block text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                        >
                          Markets
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Education - Desktop Only */}
                <div className="md:col-span-5 hidden md:block">
                  <h3 className="font-semibold text-gray-900 mb-3">
                    Education
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    {educationProducts.map((product, index) => (
                      <a
                        key={index}
                        href={product.url}
                        className="text-center group hover:bg-gray-50 p-3 rounded-lg transition-colors duration-200"
                      >
                        <img
                          src={product.image}
                          alt={product.name}
                          className="h-10 mx-auto mb-2"
                        />
                        <div className="text-sm text-gray-900">
                          {product.name}
                        </div>
                      </a>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navigation;
